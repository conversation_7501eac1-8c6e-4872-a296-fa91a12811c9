const XLSX = require('xlsx');
const semver = require('semver');
const path = require('path');
const fs = require('fs');

/**
 * 读取 Excel 文件并解析数据
 * @param {string} filePath - Excel 文件路径
 * @returns {Array} 解析后的数据数组
 */
function readExcelFile(filePath) {
    try {
        console.log('正在读取文件:', filePath);
        
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }
        
        // 读取 Excel 文件
        const workbook = XLSX.readFile(filePath);
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 转换为 JSON 格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length === 0) {
            throw new Error('Excel 文件为空');
        }
        
        // 获取表头
        const headers = jsonData[0];
        console.log('检测到的列:', headers);
        
        // 转换为对象数组，跳过表头
        const dataRows = jsonData.slice(1).map((row, index) => {
            const rowData = {};
            headers.forEach((header, colIndex) => {
                rowData[header] = row[colIndex] || '';
            });
            rowData._originalIndex = index + 1; // 保存原始行号（从1开始，不包括表头）
            return rowData;
        }).filter(row => {
            // 过滤掉空行（包名称为空的行）
            return row.name && row.name.trim() !== '';
        });
        
        console.log(`成功读取 ${dataRows.length} 行有效数据`);
        return { headers, data: dataRows };
        
    } catch (error) {
        console.error('读取 Excel 文件时出错:', error.message);
        throw error;
    }
}

/**
 * 验证版本号格式
 * @param {string} version - 版本号字符串
 * @returns {boolean} 是否为有效的 semver 版本号
 */
function isValidSemver(version) {
    if (!version || typeof version !== 'string') {
        return false;
    }

    // 清理版本号字符串
    const cleanVersion = version.trim();

    // 使用 semver 库验证
    return semver.valid(cleanVersion) !== null;
}

/**
 * 清理和标准化版本号
 * @param {string} version - 原始版本号
 * @returns {string} 清理后的版本号
 */
function cleanVersion(version) {
    if (!version || typeof version !== 'string') {
        return '0.0.0';
    }

    let cleanedVersion = version.trim();

    // 处理特殊格式：将 "7.0.0 - rc.1" 转换为 "7.0.0-rc.1"
    cleanedVersion = cleanedVersion.replace(/\s+-\s+/, '-');

    // 如果不是有效的 semver，尝试修复
    if (!semver.valid(cleanedVersion)) {
        // 移除前缀字符（如 v, ^, ~, >=, 等）
        cleanedVersion = cleanedVersion.replace(/^[v^~>=<]+/, '');

        // 再次检查是否有效
        if (!semver.valid(cleanedVersion)) {
            console.warn(`无效的版本号: "${version}"，使用默认版本 0.0.0`);
            return '0.0.0';
        }
    }

    return cleanedVersion;
}

/**
 * 判断是否为数字型预发布版本（如 7.0.0-2）
 * 这种格式被视为正式版本的一种
 * @param {Array} prerelease - 预发布数组
 * @returns {boolean} 是否为数字型预发布版本
 */
function isNumericPrerelease(prerelease) {
    return prerelease.length === 1 && typeof prerelease[0] === 'number';
}

/**
 * 自定义版本号比较函数，正确处理预发布版本
 * 排序规则：数字型预发布版本（如7.0.0-2）> 正式版 > rc > beta > alpha
 * @param {string} versionA - 版本号A
 * @param {string} versionB - 版本号B
 * @returns {number} 比较结果（降序：最新版本在前）
 */
function compareVersions(versionA, versionB) {
    const cleanA = cleanVersion(versionA);
    const cleanB = cleanVersion(versionB);

    try {
        const parsedA = semver.parse(cleanA);
        const parsedB = semver.parse(cleanB);

        if (!parsedA || !parsedB) {
            // 如果解析失败，使用基本的 semver 比较
            return semver.rcompare(cleanA, cleanB);
        }

        // 比较主版本号
        const majorCompare = parsedB.major - parsedA.major;
        if (majorCompare !== 0) return majorCompare;

        // 比较次版本号
        const minorCompare = parsedB.minor - parsedA.minor;
        if (minorCompare !== 0) return minorCompare;

        // 比较修订版本号
        const patchCompare = parsedB.patch - parsedA.patch;
        if (patchCompare !== 0) return patchCompare;

        // 如果基本版本号相同，处理预发布版本
        const isNumericA = isNumericPrerelease(parsedA.prerelease);
        const isNumericB = isNumericPrerelease(parsedB.prerelease);
        const isReleaseA = parsedA.prerelease.length === 0;
        const isReleaseB = parsedB.prerelease.length === 0;

        // 数字型预发布版本 > 正式版 > 其他预发布版本
        if (isNumericA && !isNumericB) {
            return -1; // A是数字型预发布版本，优先级最高
        }
        if (!isNumericA && isNumericB) {
            return 1; // B是数字型预发布版本，优先级最高
        }

        // 如果都是数字型预发布版本，比较数字大小
        if (isNumericA && isNumericB) {
            return parsedB.prerelease[0] - parsedA.prerelease[0];
        }

        // 正式版 > 其他预发布版本
        if (isReleaseA && !isReleaseB && !isNumericB) {
            return -1; // A是正式版，B是其他预发布版
        }
        if (!isReleaseA && isReleaseB && !isNumericA) {
            return 1; // B是正式版，A是其他预发布版
        }

        // 如果都是其他预发布版本，按照 rc > beta > alpha 的顺序
        if (!isReleaseA && !isReleaseB && !isNumericA && !isNumericB) {
            const prereleaseOrder = { 'rc': 3, 'beta': 2, 'alpha': 1 };

            const typeA = String(parsedA.prerelease[0]).toLowerCase();
            const typeB = String(parsedB.prerelease[0]).toLowerCase();

            const orderA = prereleaseOrder[typeA] || 0;
            const orderB = prereleaseOrder[typeB] || 0;

            if (orderA !== orderB) {
                return orderB - orderA; // 降序：rc > beta > alpha
            }

            // 如果预发布类型相同，比较版本号
            if (parsedA.prerelease.length > 1 && parsedB.prerelease.length > 1) {
                const numA = parseInt(parsedA.prerelease[1]) || 0;
                const numB = parseInt(parsedB.prerelease[1]) || 0;
                return numB - numA; // 降序：数字大的在前
            }
        }

        // 如果完全相同，返回0
        return 0;

    } catch (error) {
        console.warn(`版本比较出错: ${versionA} vs ${versionB}`, error.message);
        // 如果 semver 比较失败，使用字符串比较作为后备
        return versionB.localeCompare(versionA);
    }
}

/**
 * 对数据进行双重排序
 * @param {Array} data - 要排序的数据数组
 * @returns {Array} 排序后的数据数组
 */
function sortData(data) {
    console.log('开始排序数据...');

    const sortedData = [...data].sort((a, b) => {
        // 主排序：包名称按字母升序
        const nameA = (a.name || '').toLowerCase();
        const nameB = (b.name || '').toLowerCase();

        if (nameA < nameB) return -1;
        if (nameA > nameB) return 1;

        // 次排序：相同包名下，版本号按自定义规则降序（最新版本在前）
        return compareVersions(a.version, b.version);
    });

    console.log('排序完成');
    return sortedData;
}

/**
 * 显示排序统计信息
 * @param {Array} data - 排序后的数据
 */
function showSortingStats(data) {
    console.log('\n=== 排序统计信息 ===');

    // 统计包的数量
    const packageCounts = {};
    data.forEach(row => {
        const packageName = row.name;
        packageCounts[packageName] = (packageCounts[packageName] || 0) + 1;
    });

    const uniquePackages = Object.keys(packageCounts).length;
    const totalRows = data.length;
    const duplicatePackages = Object.entries(packageCounts)
        .filter(([name, count]) => count > 1)
        .length;

    console.log(`总行数: ${totalRows}`);
    console.log(`唯一包数量: ${uniquePackages}`);
    console.log(`有多个版本的包数量: ${duplicatePackages}`);

    // 显示有多个版本的包
    if (duplicatePackages > 0) {
        console.log('\n有多个版本的包:');
        Object.entries(packageCounts)
            .filter(([name, count]) => count > 1)
            .forEach(([name, count]) => {
                console.log(`  ${name}: ${count} 个版本`);
            });
    }
}

/**
 * 将排序后的数据写入新的 Excel 文件
 * @param {Array} headers - 表头数组
 * @param {Array} data - 排序后的数据数组
 * @param {string} outputPath - 输出文件路径
 */
function writeExcelFile(headers, data, outputPath) {
    try {
        console.log('正在写入文件:', outputPath);

        // 准备数据：将对象数组转换为二维数组
        const worksheetData = [headers]; // 添加表头

        data.forEach(row => {
            const rowArray = headers.map(header => row[header] || '');
            worksheetData.push(rowArray);
        });

        // 创建工作表
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

        // 设置列宽（可选，让内容更易读）
        const colWidths = headers.map(header => {
            let maxWidth = header.length;
            data.forEach(row => {
                const cellValue = String(row[header] || '');
                maxWidth = Math.max(maxWidth, cellValue.length);
            });
            return { wch: Math.min(maxWidth + 2, 50) }; // 限制最大宽度为50
        });
        worksheet['!cols'] = colWidths;

        // 创建工作簿
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

        // 写入文件
        XLSX.writeFile(workbook, outputPath);

        console.log(`成功写入 ${data.length} 行数据到文件: ${outputPath}`);

    } catch (error) {
        console.error('写入 Excel 文件时出错:', error.message);
        throw error;
    }
}

/**
 * 验证输出文件路径
 * @param {string} outputPath - 输出文件路径
 * @returns {string} 验证后的输出文件路径
 */
function validateOutputPath(outputPath) {
    if (!outputPath) {
        throw new Error('输出文件路径不能为空');
    }

    // 确保文件扩展名为 .xlsx
    if (!outputPath.toLowerCase().endsWith('.xlsx')) {
        outputPath += '.xlsx';
    }

    // 检查输出目录是否存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
        throw new Error(`输出目录不存在: ${outputDir}`);
    }

    return outputPath;
}

/**
 * 验证数据格式
 * @param {Array} headers - 表头数组
 * @param {Array} data - 数据数组
 */
function validateData(headers, data) {
    console.log('正在验证数据格式...');

    // 检查表头
    if (!headers || !Array.isArray(headers) || headers.length === 0) {
        throw new Error('表头数据无效');
    }

    // 检查必需的列
    const requiredColumns = ['name', 'version'];
    const missingColumns = requiredColumns.filter(col => !headers.includes(col));
    if (missingColumns.length > 0) {
        throw new Error(`缺少必需的列: ${missingColumns.join(', ')}`);
    }

    // 检查数据
    if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error('没有有效的数据行');
    }

    // 验证每行数据
    const invalidRows = [];
    data.forEach((row, index) => {
        const issues = [];

        // 检查包名称
        if (!row.name || typeof row.name !== 'string' || row.name.trim() === '') {
            issues.push('包名称为空');
        }

        // 检查版本号
        if (!row.version || typeof row.version !== 'string' || row.version.trim() === '') {
            issues.push('版本号为空');
        } else {
            const cleanedVersion = cleanVersion(row.version);
            if (!isValidSemver(cleanedVersion)) {
                issues.push(`版本号格式无效: ${row.version}`);
            }
        }

        if (issues.length > 0) {
            invalidRows.push({
                rowIndex: index + 2, // +2 因为从1开始计数且跳过表头
                packageName: row.name || '未知',
                issues: issues
            });
        }
    });

    // 报告验证结果
    if (invalidRows.length > 0) {
        console.warn(`发现 ${invalidRows.length} 行数据有问题:`);
        invalidRows.slice(0, 10).forEach(row => { // 只显示前10个问题
            console.warn(`  第${row.rowIndex}行 (${row.packageName}): ${row.issues.join(', ')}`);
        });

        if (invalidRows.length > 10) {
            console.warn(`  ... 还有 ${invalidRows.length - 10} 行有问题`);
        }

        // 可以选择是否继续处理
        console.warn('将尝试修复这些问题并继续处理...');
    }

    console.log('数据验证完成');
}

module.exports = {
    readExcelFile,
    isValidSemver,
    cleanVersion,
    isNumericPrerelease,
    compareVersions,
    sortData,
    showSortingStats,
    writeExcelFile,
    validateOutputPath,
    validateData
};
