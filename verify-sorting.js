const XLSX = require('xlsx');
const semver = require('semver');

function verifySorting(filePath) {
    console.log('正在验证排序结果:', filePath);
    
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log('\n=== 排序后的数据 ===');
    data.forEach((row, index) => {
        if (index === 0) {
            console.log(`表头: ${row.join(' | ')}`);
            console.log('---');
        } else {
            console.log(`${index}: ${row[0]} | ${row[1]} | ${row[2] || ''}`);
        }
    });
    
    // 验证排序逻辑
    console.log('\n=== 验证排序逻辑 ===');
    let isCorrectlySorted = true;
    
    for (let i = 2; i < data.length; i++) { // 从第2行开始（跳过表头）
        const currentRow = data[i];
        const previousRow = data[i - 1];
        
        const currentName = currentRow[0];
        const previousName = previousRow[0];
        const currentVersion = currentRow[1];
        const previousVersion = previousRow[1];
        
        // 检查包名称排序
        if (currentName < previousName) {
            console.error(`❌ 包名称排序错误: ${previousName} 应该在 ${currentName} 之后`);
            isCorrectlySorted = false;
        }
        
        // 如果是相同包名，检查版本号排序
        if (currentName === previousName) {
            try {
                const cleanCurrent = currentVersion.replace(/^[v^~>=<]+/, '');
                const cleanPrevious = previousVersion.replace(/^[v^~>=<]+/, '');
                
                if (semver.valid(cleanCurrent) && semver.valid(cleanPrevious)) {
                    if (semver.gt(cleanCurrent, cleanPrevious)) {
                        console.error(`❌ 版本号排序错误: ${previousVersion} 应该在 ${currentVersion} 之后`);
                        isCorrectlySorted = false;
                    }
                }
            } catch (error) {
                console.warn(`⚠️  版本号比较失败: ${previousVersion} vs ${currentVersion}`);
            }
        }
    }
    
    if (isCorrectlySorted) {
        console.log('✅ 排序验证通过！');
    } else {
        console.log('❌ 排序验证失败！');
    }
    
    return isCorrectlySorted;
}

// 验证排序结果
verifySorting('final-sorted-npm-test.xlsx');
