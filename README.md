# NPM 包数据排序工具

一个用于处理 Excel 文件中 npm 包数据的 Node.js 工具，支持按包名称和版本号进行双重排序。

## 功能特性

- ✅ **双重排序**：主排序按包名称字母升序，次排序按版本号 semver 降序
- ✅ **数据完整性**：排序时保持每行数据的完整性
- ✅ **版本号处理**：使用 semver 库进行标准的语义化版本比较
- ✅ **错误处理**：完善的文件验证和数据格式检查
- ✅ **命令行界面**：友好的命令行交互体验
- ✅ **自动文件命名**：支持自动生成输出文件名

## 安装依赖

```bash
npm install
```

## 使用方法

### 基本用法

```bash
# 指定输入和输出文件
node index.js npm-test.xlsx sorted-npm-test.xlsx

# 只指定输入文件，输出文件自动命名为 sorted_<输入文件名>
node index.js npm-test.xlsx

# 查看帮助信息
node index.js --help
```

### 使用 npm scripts

```bash
# 使用默认测试文件
npm test

# 查看帮助
npm run help
```

## 输入文件格式

Excel 文件应包含以下列：

| 列名 | 说明 | 必需 |
|------|------|------|
| name | npm 包名称 | ✅ |
| version | 包版本号 | ✅ |
| 其他列 | 其他包信息 | ❌ |

### 示例数据

```
name                    | version  | 入标准库
@babel/core            | 7.26.0   | √
@babel/core            | 7.14.6   | √
@alicloud/arms20190808 | 1.0.2    | √
```

## 排序规则

1. **主排序**：按包名称（name 列）字母升序排列
2. **次排序**：相同包名下，按版本号（version 列）semver 降序排列（最新版本在前）

### 版本号优先级规则

版本号排序遵循以下优先级（从高到低）：
1. **数字型预发布版本**（如 `7.0.0-2`）- 被视为正式版本的扩展
2. **正式版本**（如 `7.0.0`）
3. **rc 版本**（如 `7.0.0-rc.1`）
4. **beta 版本**（如 `7.0.0-beta.1`）
5. **alpha 版本**（如 `7.0.0-alpha.1`）

### 排序示例

**排序前：**
```
@babel/core     | 7.0.0-rc.1
@babel/core     | 7.14.6
@alicloud/arms  | 1.0.2
@babel/core     | 7.0.0-2
@babel/core     | 7.0.0
@babel/core     | 7.26.0
```

**排序后：**
```
@alicloud/arms  | 1.0.2
@babel/core     | 7.26.0
@babel/core     | 7.14.6
@babel/core     | 7.0.0-2
@babel/core     | 7.0.0
@babel/core     | 7.0.0-rc.1
```

## 版本号处理

- 支持标准的 semver 版本号格式（如 `1.2.3`）
- 支持预发布版本（如 `1.0.0-alpha.1`, `1.0.0-beta.2`, `1.0.0-rc.3`）
- 支持数字型预发布版本（如 `7.0.0-2`），被视为正式版本的扩展
- 自动处理特殊格式（如 `7.0.0 - rc.1` 转换为 `7.0.0-rc.1`）
- 自动清理版本号前缀（如 `v1.2.3`, `^1.2.3`, `~1.2.3`）
- 完善的版本号比较逻辑，正确处理各种预发布版本的优先级

## 错误处理

工具包含完善的错误处理机制：

- ✅ 文件存在性检查
- ✅ Excel 文件格式验证
- ✅ 必需列检查（name, version）
- ✅ 数据格式验证
- ✅ 版本号格式验证
- ✅ 输出目录检查

## 项目结构

```
npm-analyze/
├── index.js              # 主程序入口
├── npm-analyzer.js       # 核心处理逻辑
├── package.json          # 项目配置
├── npm-test.xlsx         # 测试数据文件
├── analyze-structure.js  # 文件结构分析工具
├── verify-sorting.js     # 排序结果验证工具
└── README.md            # 项目文档
```

## 开发和测试

### 分析文件结构
```bash
node analyze-structure.js
```

### 验证排序结果
```bash
node verify-sorting.js
```

## 依赖包

- **xlsx**: Excel 文件读写
- **semver**: 语义化版本号处理

## 许可证

ISC

## 更新日志

### v1.0.0
- 初始版本发布
- 支持双重排序功能
- 完善的错误处理
- 命令行界面
